import React, { useState } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import "../../styles/Signup.css";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "react-icons/fa";
import { FaMobile } from "react-icons/fa6";
import {
  register,
  reset,
  googleSignIn,
  googleSignUp,
} from "../../redux/slices/authSlice";
import toast from "../../utils/toast";
import GoogleSignInButton from "../../components/common/GoogleSignInButton";
import firebaseService from "../../services/firebaseService";

const Signup = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { isLoading, isError, isSuccess, error } = useSelector(
    (state) => state.auth
  );

  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    countryCode: "+91",
    accountType: "learn",
    agreeToTerms: false,
  });

  const [errors, setErrors] = useState({});

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value,
    });

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: null,
      });
    }
  };

  const handleAccountTypeChange = (type) => {
    setFormData({
      ...formData,
      accountType: type,
    });
  };

  const handleCountryCodeChange = (e) => {
    setFormData({
      ...formData,
      countryCode: e.target.value,
    });
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.firstName.trim()) {
      newErrors.firstName = "First name is required";
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = "Last name is required";
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Email is invalid";
    }

    if (!formData.phone.trim()) {
      newErrors.phone = "Phone number is required";
    } else if (!/^\d{10}$/.test(formData.phone)) {
      newErrors.phone = "Phone number must be 10 digits";
    }

    if (!formData.agreeToTerms) {
      newErrors.agreeToTerms = "You must agree to the terms and conditions";
    }

    return newErrors;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    // Reset any previous errors
    dispatch(reset());

    try {
      // Prepare registration data
      const registrationData = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        mobile: `${formData.countryCode}${formData.phone}`,
        role: formData.accountType === "learn" ? "buyer" : "seller",
      };

      // Dispatch register action and wait for response
      const result = await dispatch(register(registrationData)).unwrap();

      // Show success message
      toast.auth.registrationSuccess();

      // Navigate to OTP verification page with user ID and phone number
      navigate("/otp-verification", {
        state: {
          userId: result.userId,
          phoneNumber: `${formData.countryCode} ${formData.phone}`,
          cooldownSeconds: result.cooldownSeconds || 60,
          isLogin: false,
          developmentOtp: result.developmentOtp, // Pass development OTP if available
        },
      });
    } catch (error) {
      // Handle different types of errors
      if (error.includes("already registered")) {
        toast.error(
          "This email or mobile number is already registered. Please try logging in instead."
        );
      } else {
        toast.api.error({ response: { data: { message: error } } });
      }
    }
  };

  // Handle Google Sign-Up
  const handleGoogleSignUp = async () => {
    try {
      dispatch(reset());

      // Check if Firebase is initialized
      if (!firebaseService.isInitialized()) {
        toast.error(
          "Firebase is not initialized. Please check your configuration."
        );
        return;
      }

      // Sign in with Google using Firebase
      const result = await firebaseService.signInWithGoogle();

      // Check if user already exists
      try {
        const response = await dispatch(googleSignIn(result.idToken)).unwrap();

        // User already exists - redirect to login
        toast.info("Account already exists. Redirecting to dashboard...");

        // Navigate based on user role
        if (response.user.role === "buyer") {
          navigate("/buyer/dashboard");
        } else if (response.user.role === "seller") {
          navigate("/seller/dashboard");
        } else if (response.user.role === "admin") {
          navigate("/admin/dashboard");
        } else {
          navigate("/");
        }
      } catch (signInError) {
        // User doesn't exist - create new account with selected role
        const errorMessage =
          typeof signInError === "string"
            ? signInError
            : signInError?.message || "";
        if (
          errorMessage.includes("not found") ||
          errorMessage.includes("does not exist")
        ) {
          const role = formData.accountType === "learn" ? "buyer" : "seller";

          try {
            const response = await dispatch(
              googleSignUp({ idToken: result.idToken, role })
            ).unwrap();

            // Success - user created and logged in
            toast.auth.registrationSuccess();

            // Navigate based on selected role
            if (role === "buyer") {
              navigate("/buyer/dashboard");
            } else if (role === "seller") {
              navigate("/seller/dashboard");
            } else {
              navigate("/");
            }
          } catch (signUpError) {
            throw signUpError;
          }
        } else {
          throw signInError;
        }
      }
    } catch (error) {
      console.error("Google sign-up error:", error);
      const errorMessage =
        typeof error === "string"
          ? error
          : error?.message ||
            "Failed to sign up with Google. Please try again.";
      toast.error(errorMessage);
    }
  };

  return (
    <div className="signup__page">
      <div className="signup__container">
        <h1 className="signup__title">Sign up to your account</h1>

        <div className="signup__account-type">
          <p className="signup__label">Select Account Type</p>
          <div className="signup__options">
            <div
              className={`signup__option ${
                formData.accountType === "learn"
                  ? "signup__option--selected"
                  : ""
              }`}
              onClick={() => handleAccountTypeChange("learn")}
            >
              <div className="signup__option-checkbox">
                {formData.accountType === "learn" && (
                  <div className="signup__option-check">
                    <FaCheck className="signup__check-icon" />
                  </div>
                )}
              </div>
              <div className="signup__option-content">
                <div className="signup__option-icon">
                  <FaBook />
                </div>
                <p className="signup__option-text">I want to learn</p>
              </div>
            </div>

            <div
              className={`signup__option ${
                formData.accountType === "teach"
                  ? "signup__option--selected"
                  : ""
              }`}
              onClick={() => handleAccountTypeChange("teach")}
            >
              <div className="signup__option-checkbox">
                {formData.accountType === "teach" && (
                  <div className="signup__option-check">
                    <FaCheck className="signup__check-icon" />
                  </div>
                )}
              </div>
              <div className="signup__option-content">
                <div className="signup__option-icon">
                  <FaChalkboardTeacher />
                </div>
                <p className="signup__option-text">I want to teach</p>
              </div>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="signup__form">
          <div className="signup__form-row">
            <div className="signup__input-container">
              <div className="signup__input-icon">
                <FaUser />
              </div>
              <input
                type="text"
                id="firstName"
                name="firstName"
                value={formData.firstName}
                onChange={handleChange}
                placeholder="First Name"
                className={`signup__input ${
                  errors.firstName ? "signup__input--error" : ""
                }`}
                required
              />
              {errors.firstName && (
                <p className="signup__error">{errors.firstName}</p>
              )}
            </div>

            <div className="signup__input-container">
              <div className="signup__input-icon">
                <FaUser />
              </div>
              <input
                type="text"
                id="lastName"
                name="lastName"
                value={formData.lastName}
                onChange={handleChange}
                placeholder="Last Name"
                className={`signup__input ${
                  errors.lastName ? "signup__input--error" : ""
                }`}
                required
              />
              {errors.lastName && (
                <p className="signup__error">{errors.lastName}</p>
              )}
            </div>
          </div>

          <div className="signup__input-container">
            <div className="signup__input-icon">
              <FaEnvelope />
            </div>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="Enter Email Address"
              className={`signup__input ${
                errors.email ? "signup__input--error" : ""
              }`}
              required
            />
            {errors.email && <p className="signup__error">{errors.email}</p>}
          </div>

          <div className="signup__form-input signup__phone-container">
            <div className="signup__phone-wrapper">
              <div>
                <div className="signup__country-code-select">
                  <FaMobile style={{ color: "var(--dark-gray)" }} />
                  <select
                    value={formData.countryCode}
                    onChange={handleCountryCodeChange}
                    className="selectstylesnone"
                  >
                    <option value="+91">+91</option>
                    <option value="+1">+1</option>
                    <option value="+44">+44</option>
                    <option value="+61">+61</option>
                    <option value="+86">+86</option>
                    <option value="+49">+49</option>
                    <option value="+33">+33</option>
                    <option value="+81">+81</option>
                    <option value="+7">+7</option>
                    <option value="+55">+55</option>
                  </select>
                </div>
              </div>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={(e) => {
                  const phoneValue = e.target.value.replace(/\D/g, "");
                  handleChange({
                    target: {
                      name: "phone",
                      value: phoneValue,
                    },
                  });
                }}
                placeholder="00000 00000"
                className={`signup__form-input signup__phone-input ${
                  errors.phone ? "signup__input--error" : ""
                }`}
                required
                pattern="[0-9]*"
              />
            </div>
            {errors.phone && <p className="error-message">{errors.phone}</p>}
          </div>

          <div className="signup__terms">
            <input
              type="checkbox"
              id="agreeToTerms"
              name="agreeToTerms"
              checked={formData.agreeToTerms}
              onChange={handleChange}
              className="signup__checkbox"
            />
            <label htmlFor="agreeToTerms" className="signup__terms-label">
              By sign up you agree to our{" "}
              <Link to="/terms" className="signup__terms-link">
                Terms & Conditions
              </Link>
            </label>
            {errors.agreeToTerms && (
              <p className="signup__error">{errors.agreeToTerms}</p>
            )}
          </div>

          <button type="submit" className="signup__button" disabled={isLoading}>
            {isLoading ? "Creating Account..." : "Create Your Account"}
          </button>

          <div className="signup__divider">
            <span>or</span>
          </div>

          <GoogleSignInButton
            onClick={handleGoogleSignUp}
            isLoading={isLoading}
            text="Sign up with Google"
            variant="secondary"
          />

          <p className="signup__login-link mt-10">
            Do you have an account?{" "}
            <Link to="/auth" className="signup__link">
              Sign In
            </Link>
          </p>
        </form>
      </div>
    </div>
  );
};

export default Signup;
