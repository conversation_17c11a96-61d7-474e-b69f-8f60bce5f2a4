import React, { useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { selectProfile } from "../../redux/slices/sellerDashboardSlice";
import SellerLayout from "../../components/seller/SellerLayout";
import "../../styles/SellerSettings.css";

// Icons
import { FaUser, FaCreditCard, FaBell, FaShield, FaSave, FaEdit } from "react-icons/fa";

const SellerSettings = () => {
  const dispatch = useDispatch();
  const profile = useSelector(selectProfile);
  const [activeTab, setActiveTab] = useState('profile');
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    firstName: profile.firstName,
    lastName: profile.lastName,
    email: profile.email,
    phone: profile.phone,
    businessName: profile.businessName,
    businessType: profile.businessType,
  });

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    // Dispatch update action here
    console.log('Updating profile:', formData);
    setIsEditing(false);
  };

  // Settings tabs
  const settingsTabs = [
    { id: 'profile', label: 'Profile', icon: <FaUser /> },
    { id: 'payment', label: 'Payment', icon: <FaCreditCard /> },
    { id: 'notifications', label: 'Notifications', icon: <FaBell /> },
    { id: 'security', label: 'Security', icon: <FaShield /> },
  ];

  // Render profile settings
  const renderProfileSettings = () => (
    <div className="SellerSettings__section">
      <div className="SellerSettings__sectionHeader">
        <h3 className="SellerSettings__sectionTitle">Profile Information</h3>
        <button
          className="SellerSettings__editBtn"
          onClick={() => setIsEditing(!isEditing)}
        >
          <FaEdit />
          <span>{isEditing ? 'Cancel' : 'Edit'}</span>
        </button>
      </div>

      <form onSubmit={handleSubmit} className="SellerSettings__form">
        <div className="SellerSettings__formGrid">
          <div className="SellerSettings__formGroup">
            <label className="SellerSettings__label">First Name</label>
            <input
              type="text"
              name="firstName"
              value={formData.firstName}
              onChange={handleInputChange}
              disabled={!isEditing}
              className="SellerSettings__input"
            />
          </div>

          <div className="SellerSettings__formGroup">
            <label className="SellerSettings__label">Last Name</label>
            <input
              type="text"
              name="lastName"
              value={formData.lastName}
              onChange={handleInputChange}
              disabled={!isEditing}
              className="SellerSettings__input"
            />
          </div>

          <div className="SellerSettings__formGroup">
            <label className="SellerSettings__label">Email</label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              disabled={!isEditing}
              className="SellerSettings__input"
            />
          </div>

          <div className="SellerSettings__formGroup">
            <label className="SellerSettings__label">Phone</label>
            <input
              type="tel"
              name="phone"
              value={formData.phone}
              onChange={handleInputChange}
              disabled={!isEditing}
              className="SellerSettings__input"
            />
          </div>

          <div className="SellerSettings__formGroup">
            <label className="SellerSettings__label">Business Name</label>
            <input
              type="text"
              name="businessName"
              value={formData.businessName}
              onChange={handleInputChange}
              disabled={!isEditing}
              className="SellerSettings__input"
            />
          </div>

          <div className="SellerSettings__formGroup">
            <label className="SellerSettings__label">Business Type</label>
            <select
              name="businessType"
              value={formData.businessType}
              onChange={handleInputChange}
              disabled={!isEditing}
              className="SellerSettings__select"
            >
              <option value="Individual Creator">Individual Creator</option>
              <option value="Small Business">Small Business</option>
              <option value="Company">Company</option>
              <option value="Organization">Organization</option>
            </select>
          </div>
        </div>

        {isEditing && (
          <div className="SellerSettings__formActions">
            <button type="submit" className="SellerSettings__saveBtn">
              <FaSave />
              <span>Save Changes</span>
            </button>
          </div>
        )}
      </form>
    </div>
  );

  // Render payment settings
  const renderPaymentSettings = () => (
    <div className="SellerSettings__section">
      <div className="SellerSettings__sectionHeader">
        <h3 className="SellerSettings__sectionTitle">Payment Settings</h3>
      </div>
      <div className="SellerSettings__placeholder">
        <p>Payment settings will be implemented here.</p>
        <p>This will include bank account details, PayPal integration, and payout preferences.</p>
      </div>
    </div>
  );

  // Render notification settings
  const renderNotificationSettings = () => (
    <div className="SellerSettings__section">
      <div className="SellerSettings__sectionHeader">
        <h3 className="SellerSettings__sectionTitle">Notification Preferences</h3>
      </div>
      <div className="SellerSettings__placeholder">
        <p>Notification settings will be implemented here.</p>
        <p>This will include email notifications, SMS alerts, and push notification preferences.</p>
      </div>
    </div>
  );

  // Render security settings
  const renderSecuritySettings = () => (
    <div className="SellerSettings__section">
      <div className="SellerSettings__sectionHeader">
        <h3 className="SellerSettings__sectionTitle">Security Settings</h3>
      </div>
      <div className="SellerSettings__placeholder">
        <p>Security settings will be implemented here.</p>
        <p>This will include password change, two-factor authentication, and login history.</p>
      </div>
    </div>
  );

  // Render active tab content
  const renderTabContent = () => {
    switch (activeTab) {
      case 'profile':
        return renderProfileSettings();
      case 'payment':
        return renderPaymentSettings();
      case 'notifications':
        return renderNotificationSettings();
      case 'security':
        return renderSecuritySettings();
      default:
        return renderProfileSettings();
    }
  };

  return (
    <SellerLayout>
      <div className="SellerSettings">
        {/* Settings Navigation */}
        <div className="SellerSettings__nav">
          {settingsTabs.map((tab) => (
            <button
              key={tab.id}
              className={`SellerSettings__navBtn ${
                activeTab === tab.id ? 'SellerSettings__navBtn--active' : ''
              }`}
              onClick={() => setActiveTab(tab.id)}
            >
              <span className="SellerSettings__navIcon">{tab.icon}</span>
              <span className="SellerSettings__navLabel">{tab.label}</span>
            </button>
          ))}
        </div>

        {/* Settings Content */}
        <div className="SellerSettings__content">
          {renderTabContent()}
        </div>
      </div>
    </SellerLayout>
  );
};

export default SellerSettings;
