const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
let firebaseApp;

const initializeFirebase = () => {
  if (!firebaseApp) {
    try {
      // Check if Firebase is configured
      if (!process.env.FIREBASE_PROJECT_ID) {
        console.warn('Firebase configuration not found. Google authentication will not work until properly configured.');
        return null;
      }

      // Check if service account key is provided
      if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
        try {
          // Parse the service account key from environment variable
          const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY);

          firebaseApp = admin.initializeApp({
            credential: admin.credential.cert(serviceAccount),
            projectId: process.env.FIREBASE_PROJECT_ID,
          });
        } catch (parseError) {
          console.error('Error parsing Firebase service account key:', parseError);
          console.warn('Using default credentials instead...');

          // Fallback to default credentials
          firebaseApp = admin.initializeApp({
            projectId: process.env.FIREBASE_PROJECT_ID,
          });
        }
      } else {
        // Use default credentials (for development or when deployed on Google Cloud)
        firebaseApp = admin.initializeApp({
          projectId: process.env.FIREBASE_PROJECT_ID,
        });
      }

      console.log('Firebase Admin SDK initialized successfully');
    } catch (error) {
      console.error('Error initializing Firebase Admin SDK:', error);
      console.warn('Google authentication will not work until Firebase is properly configured.');
      return null;
    }
  }

  return firebaseApp;
};

// Verify Firebase ID token
const verifyIdToken = async (idToken) => {
  try {
    if (!firebaseApp) {
      const app = initializeFirebase();
      if (!app) {
        throw new Error('Firebase is not configured');
      }
    }

    const decodedToken = await admin.auth().verifyIdToken(idToken);
    return decodedToken;
  } catch (error) {
    console.error('Error verifying Firebase ID token:', error);
    if (error.message === 'Firebase is not configured') {
      throw new Error('Google authentication is not available. Please contact support.');
    }
    throw new Error('Invalid or expired token');
  }
};

// Get user by UID
const getUserByUid = async (uid) => {
  try {
    if (!firebaseApp) {
      initializeFirebase();
    }

    const userRecord = await admin.auth().getUser(uid);
    return userRecord;
  } catch (error) {
    console.error('Error getting user by UID:', error);
    throw new Error('User not found');
  }
};

module.exports = {
  initializeFirebase,
  verifyIdToken,
  getUserByUid,
  admin,
};
