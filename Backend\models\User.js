const mongoose = require('mongoose');
const jwt = require('jsonwebtoken');

const UserSchema = new mongoose.Schema(
  {
    firstName: {
      type: String,
      required: [true, 'Please add a first name'],
      trim: true,
      maxlength: [50, 'First name cannot be more than 50 characters']
    },
    lastName: {
      type: String,
      required: [true, 'Please add a last name'],
      trim: true,
      maxlength: [50, 'Last name cannot be more than 50 characters']
    },
    email: {
      type: String,
      required: [true, 'Please add an email'],
      unique: true,
      match: [
        /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/,
        'Please add a valid email'
      ]
    },
    mobile: {
      type: String,
      required: function() {
        // Mobile is required for regular users but optional for Google users
        return !this.email || this.email === '';
      },
      maxlength: [20, 'Mobile number cannot be longer than 20 characters']
    },
    role: {
      type: String,
      enum: ['buyer', 'seller', 'admin'],
      default: 'buyer'
    },
    profileImage: {
      type: String,
      default: 'default-profile.jpg'
    },
    bio: {
      type: String,
      maxlength: [500, 'Bio cannot be more than 500 characters']
    },
    isVerified: {
      type: Boolean,
      default: false
    },
    sellerInfo: {
      sports: [String],
      expertise: [String],
      experience: String,
      certifications: [String]
    },
    paymentInfo: {
      stripeCustomerId: String,
      stripeConnectId: String,
      defaultPaymentMethod: String
    },
    otpCode: String,
    otpExpire: Date,
    otpAttempts: {
      type: Number,
      default: 0
    },
    otpLastSent: Date,
    otpCooldownUntil: Date,
    emailVerificationToken: String,
    emailVerificationExpire: Date,
    lastLogin: Date,
    createdAt: {
      type: Date,
      default: Date.now
    }
  },
  {
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Sign JWT and return
UserSchema.methods.getSignedJwtToken = function () {
  return jwt.sign({ id: this._id }, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRE
  });
};

// Generate OTP
UserSchema.methods.generateOTP = function() {
  // Generate a 6-digit OTP
  const otp = Math.floor(100000 + Math.random() * 900000).toString();

  // Set OTP and expiration (10 minutes)
  this.otpCode = otp;
  this.otpExpire = Date.now() + 10 * 60 * 1000;
  this.otpLastSent = Date.now();

  return otp;
};

// Check if user can request new OTP (cooldown check)
UserSchema.methods.canRequestOTP = function() {
  const cooldownSeconds = parseInt(process.env.OTP_RESEND_COOLDOWN) || 60;
  const maxAttempts = parseInt(process.env.OTP_MAX_ATTEMPTS) || 3;

  // Check if user has exceeded max attempts
  if (this.otpAttempts >= maxAttempts) {
    // Reset attempts after 1 hour
    if (this.otpCooldownUntil && this.otpCooldownUntil > Date.now()) {
      return {
        canRequest: false,
        reason: 'max_attempts_exceeded',
        cooldownUntil: this.otpCooldownUntil
      };
    } else {
      // Reset attempts after cooldown period
      this.otpAttempts = 0;
      this.otpCooldownUntil = undefined;
    }
  }

  // Check cooldown period
  if (this.otpLastSent) {
    const timeSinceLastSent = Date.now() - this.otpLastSent;
    const cooldownMs = cooldownSeconds * 1000;

    if (timeSinceLastSent < cooldownMs) {
      return {
        canRequest: false,
        reason: 'cooldown_active',
        remainingTime: Math.ceil((cooldownMs - timeSinceLastSent) / 1000)
      };
    }
  }

  return { canRequest: true };
};

// Increment OTP attempts
UserSchema.methods.incrementOTPAttempts = function() {
  this.otpAttempts = (this.otpAttempts || 0) + 1;

  const maxAttempts = parseInt(process.env.OTP_MAX_ATTEMPTS) || 3;

  // Set cooldown if max attempts reached
  if (this.otpAttempts >= maxAttempts) {
    this.otpCooldownUntil = Date.now() + (60 * 60 * 1000); // 1 hour cooldown
  }
};

// Reset OTP attempts
UserSchema.methods.resetOTPAttempts = function() {
  this.otpAttempts = 0;
  this.otpCooldownUntil = undefined;
};

// Virtual for full name
UserSchema.virtual('fullName').get(function () {
  return `${this.firstName} ${this.lastName}`;
});

// Virtual for content
UserSchema.virtual('content', {
  ref: 'Content',
  localField: '_id',
  foreignField: 'seller',
  justOne: false
});

// Virtual for orders as buyer
UserSchema.virtual('purchases', {
  ref: 'Order',
  localField: '_id',
  foreignField: 'buyer',
  justOne: false
});

// Virtual for orders as seller
UserSchema.virtual('sales', {
  ref: 'Order',
  localField: '_id',
  foreignField: 'seller',
  justOne: false
});

module.exports = mongoose.model('User', UserSchema);
