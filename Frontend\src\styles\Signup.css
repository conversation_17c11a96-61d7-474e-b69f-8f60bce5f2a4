/* Signup Page Styles */
.signup__page {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - var(--navbar-height, 90px));
  padding: var(--spacing-lg, 40px) var(--spacing-md, 20px);
  background-color: var(--bg-gray);
}

.signup__container {
  background-color: var(--white);
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow-light);
  padding: var(--spacing-lg, 40px);
  width: 100%;
  max-width: var(--container-sm, 500px);
}

.signup__title {
  font-size: var(--heading4);
  font-weight: 600;
  color: var(--secondary-color);
  text-align: center;
  margin-bottom: var(--spacing-md, 20px);
}

/* Account Type Selection */
.signup__account-type {
  margin-bottom: var(--spacing-md, 20px);
}

.signup__label {
  font-size: var(--smallfont);
  font-weight: 500;
  color: var(--secondary-color);
  margin-bottom: var(--spacing-xs, 10px);
}

.signup__options {
  display: flex;
  gap: var(--spacing-sm, 15px);
  justify-content: center;
}

.signup__option {
  display: flex;
  align-items: flex-start;
  padding: var(--spacing-sm, 15px);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  width: 50%;
  gap: var(--spacing-xs, 10px);
  position: relative;
}

.signup__option--selected {
  border-color: var(--btn-color);
  background-color: rgba(238, 52, 37, 0.05);
}

.signup__option-checkbox {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border: 2px solid var(--light-gray);
  border-radius: 50%;
  margin-top: 2px;
  transition: all 0.3s ease;
  position: absolute;
  top: 5px;
  right: 5px;
}

.signup__option--selected .signup__option-checkbox {
  border-color: var(--btn-color);
  background-color: var(--btn-color);
}

.signup__option-check {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.signup__check-icon {
  color: var(--white);
  font-size: 10px;
}

.signup__option-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
}

.signup__option-icon {
  font-size: var(--heading5);
  color: var(--secondary-color);
  margin-bottom: var(--spacing-xs, 10px);
}

.signup__option-text {
  font-size: var(--smallfont);
  color: var(--secondary-color);
  text-align: center;
}

/* Form Styles */
.signup__form {
  display: flex;
  flex-direction: column;
}

.signup__form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-sm, 15px);
}

.signup__input-container {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-md, 20px);
  width: 100%;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  background-color: var(--white);
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.signup__input-container:focus-within {
  border-color: var(--btn-color);
  box-shadow: 0 0 0 2px rgba(238, 52, 37, 0.1);
}

.signup__input-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-left: var(--spacing-xs, 10px);
  color: var(--dark-gray);
  font-size: var(--basefont);
  min-width: var(--spacing-lg, 40px);
}

.signup__input {
  width: 100%;
  padding: var(--spacing-xs, 10px) var(--spacing-sm, 15px);
  border: none;
  font-size: var(--basefont);
  color: var(--text-color);
  background-color: transparent;
  outline: none;
}

.signup__input:focus {
  border-color: var(--btn-color);
  box-shadow: 0 0 0 2px rgba(238, 52, 37, 0.1);
  outline: none;
}

.signup__input::placeholder {
  color: var(--dark-gray);
  opacity: 0.7;
}

.signup__input--error {
  border-color: #ff3b30;
}

.signup__error {
  color: #ff3b30;
  font-size: var(--extrasmallfont);
  margin-top: var(--spacing-xxs, 5px);
}

/* Phone Input Styles - Matching Auth.jsx styling */
.signup__phone-container {
  margin-bottom: var(--spacing-md, 20px);
}

.signup__phone-wrapper {
  display: flex;
  width: 100%;
}

.signup__country-code-select {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 12px 10px;
  border: 1px solid var(--light-gray);
  border-right: none;
  border-radius: var(--border-radius) 0 0 var(--border-radius);
  background-color: var(--white);
  color: var(--text-color);
  font-size: var(--basefont);
  cursor: pointer;
}

.signup__country-code-select:focus {
  border-color: var(--btn-color);
  outline: none;
}

.signup__phone-input {
  flex: 1;
  border-radius: 0 var(--border-radius) var(--border-radius) 0 !important;
  padding: 12px 16px;
  border: 1px solid var(--light-gray);
  font-size: var(--basefont);
  color: var(--text-color);
  background-color: var(--white);
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.signup__phone-input:focus {
  border-color: var(--btn-color);
  box-shadow: 0 0 0 2px rgba(238, 52, 37, 0.2);
  outline: none;
}

.signup__phone-input::placeholder {
  color: var(--dark-gray);
  opacity: 0.7;
}

/* Terms Checkbox */
.signup__terms {
  display: flex;
  align-items: flex-start;
  margin-bottom: var(--spacing-md, 20px);
  position: relative;
}

.signup__checkbox {
  margin-right: var(--spacing-xs, 10px);
  margin-top: 2px;
}

.signup__terms-label {
  font-size: var(--smallfont);
  color: var(--secondary-color);
}

.signup__terms-link {
  color: var(--btn-color);
  text-decoration: none;
}

.signup__terms-link:hover {
  text-decoration: underline;
}

/* Button and Login Link */
.signup__button {
  background-color: var(--btn-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  padding: var(--spacing-sm, 15px);
  font-size: var(--basefont);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.signup__button:hover {
  background: linear-gradient(
    to bottom,
    var(--primary-color),
    var(--btn-color)
  );
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(238, 52, 37, 0.3);
}

.signup__login-link {
  text-align: center;
  font-size: var(--smallfont);
  color: var(--secondary-color);
}

.signup__link {
  color: var(--btn-color);
  text-decoration: none;
  font-weight: 500;
}

.signup__link:hover {
  text-decoration: underline;
}

.signup__divider {
  display: flex;
  align-items: center;
  margin: 20px 0;
  text-align: center;
}

.signup__divider::before,
.signup__divider::after {
  content: "";
  flex: 1;
  height: 1px;
  background: var(--light-gray);
}

.signup__divider span {
  padding: 0 16px;
  color: var(--dark-gray);
  font-size: var(--smallfont);
  font-weight: 500;
}

/* Responsive styles */
@media (max-width: 768px) {
  .signup__container {
    padding: var(--spacing-md, 20px);
  }

  .signup__form-row {
    grid-template-columns: 1fr;
    gap: 0;
  }

  .signup__country-code-select {
    padding: 10px 8px;
    font-size: calc(var(--basefont) - 1px);
  }
}

@media (max-width: 480px) {
  .signup__page {
    padding: var(--spacing-sm, 15px) var(--spacing-xs, 10px);
  }

  .signup__container {
    padding: var(--spacing-sm, 15px);
  }

  .signup__title {
    font-size: var(--heading5);
  }

  .signup__options {
    flex-direction: column;
    width: 100%;
  }

  .signup__option {
    width: 100%;
    align-items: center;
    gap: var(--spacing-sm, 15px);
  }

  .signup__option-content {
    flex-direction: row;
    align-items: center;
    gap: var(--spacing-xs, 10px);
  }

  .signup__option-icon {
    margin-bottom: 0;
  }

  .signup__country-code-select {
    padding: 8px 6px;
  }
}
