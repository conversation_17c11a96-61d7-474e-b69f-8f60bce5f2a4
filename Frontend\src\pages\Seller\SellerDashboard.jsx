import React from "react";
import { useSelector } from "react-redux";
import {
  selectStats,
  selectMyContent,
  selectOrders,
} from "../../redux/slices/sellerDashboardSlice";
import SellerLayout from "../../components/seller/SellerLayout";
import "../../styles/SellerDashboard.css";

// Icons
import { FaEye, FaEdit } from "react-icons/fa";

const SellerDashboard = () => {
  const stats = useSelector(selectStats);
  const myContent = useSelector(selectMyContent);
  const orders = useSelector(selectOrders);

  // Mock data for demonstration (matching Figma design)
  const sportsStrategies = [
    {
      id: 1,
      title: "<PERSON> and Coaching Strategies to Developing Toughness in...",
      date: "20 May 2025",
      price: "$25.00",
      status: "active",
      thumbnail: "/api/placeholder/60/60"
    },
    {
      id: 2,
      title: "<PERSON> - Early Recruiting Offensive Strategies",
      date: "20 May 2025",
      price: "$25.00",
      status: "active",
      thumbnail: "/api/placeholder/60/60"
    }
  ];

  const newRequests = [
    {
      id: "#123456789",
      title: "<PERSON> - <PERSON> and Coaching Strategies to Developing Toughness in...",
      date: "20 May 2025",
      price: "$25.00",
      requestedAmount: "$19.00",
      customer: "John Smith",
      thumbnail: "/api/placeholder/60/60"
    },
    {
      id: "#123456789",
      title: "John Calipari - Early Recruiting Offensive Strategies",
      date: "20 May 2025",
      price: "$25.00",
      requestedAmount: "$18.00",
      customer: "Chris Smith",
      thumbnail: "/api/placeholder/60/60"
    }
  ];

  const newBids = [
    {
      id: "#123456789",
      title: "Frank Martin - Daily and Coaching Strategies to Developing Toughness in...",
      date: "20 May 2025",
      price: "$25.00",
      bidAmount: "$18.00",
      thumbnail: "/api/placeholder/60/60"
    },
    {
      id: "#123456789",
      title: "John Calipari - Early Recruiting Offensive Strategies",
      date: "20 May 2025",
      price: "$25.00",
      bidAmount: "$19.00",
      thumbnail: "/api/placeholder/60/60"
    }
  ];

  return (
    <SellerLayout>
      <div className="SellerDashboard">
        {/* Stats Cards */}
        <div className="SellerDashboard__statsGrid">
          <div className="SellerDashboard__statCard SellerDashboard__statCard--purple">
            <div className="SellerDashboard__statNumber">08</div>
            <div className="SellerDashboard__statLabel">Total Strategies</div>
          </div>

          <div className="SellerDashboard__statCard SellerDashboard__statCard--orange">
            <div className="SellerDashboard__statNumber">02</div>
            <div className="SellerDashboard__statLabel">Requests</div>
          </div>

          <div className="SellerDashboard__statCard SellerDashboard__statCard--green">
            <div className="SellerDashboard__statNumber">03</div>
            <div className="SellerDashboard__statLabel">Bids</div>
          </div>
        </div>

        {/* My Sports Strategies Section */}
        <div className="SellerDashboard__section">
          <div className="SellerDashboard__sectionHeader">
            <h2 className="SellerDashboard__sectionTitle">My Sports Strategies</h2>
            <button className="SellerDashboard__viewAllBtn">View All Downloads</button>
          </div>

          <div className="SellerDashboard__table">
            <div className="SellerDashboard__tableHeader">
              <div className="SellerDashboard__tableHeaderCell">No.</div>
              <div className="SellerDashboard__tableHeaderCell">Videos/Documents</div>
              <div className="SellerDashboard__tableHeaderCell">Date</div>
              <div className="SellerDashboard__tableHeaderCell">Price</div>
              <div className="SellerDashboard__tableHeaderCell">Status</div>
              <div className="SellerDashboard__tableHeaderCell">Actions</div>
            </div>

            {sportsStrategies.map((strategy, index) => (
              <div key={strategy.id} className="SellerDashboard__tableRow">
                <div className="SellerDashboard__tableCell">{index + 1}</div>
                <div className="SellerDashboard__tableCell SellerDashboard__tableCell--content">
                  <img src={strategy.thumbnail} alt="" className="SellerDashboard__thumbnail" />
                  <span className="SellerDashboard__contentTitle">{strategy.title}</span>
                </div>
                <div className="SellerDashboard__tableCell">{strategy.date}</div>
                <div className="SellerDashboard__tableCell">{strategy.price}</div>
                <div className="SellerDashboard__tableCell">
                  <span className="SellerDashboard__statusToggle SellerDashboard__statusToggle--active"></span>
                </div>
                <div className="SellerDashboard__tableCell">
                  <button className="SellerDashboard__actionIcon">
                    <FaEye />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* New Requests Section */}
        <div className="SellerDashboard__section">
          <div className="SellerDashboard__sectionHeader">
            <h2 className="SellerDashboard__sectionTitle">New Requests</h2>
            <button className="SellerDashboard__viewAllBtn">View All Requests</button>
          </div>

          <div className="SellerDashboard__table">
            <div className="SellerDashboard__tableHeader">
              <div className="SellerDashboard__tableHeaderCell">No.</div>
              <div className="SellerDashboard__tableHeaderCell">Order Id</div>
              <div className="SellerDashboard__tableHeaderCell">Videos/Documents</div>
              <div className="SellerDashboard__tableHeaderCell">Date</div>
              <div className="SellerDashboard__tableHeaderCell">Price</div>
              <div className="SellerDashboard__tableHeaderCell">Requested Amount</div>
              <div className="SellerDashboard__tableHeaderCell">Requested Customer</div>
              <div className="SellerDashboard__tableHeaderCell">Actions</div>
            </div>

            {newRequests.map((request, index) => (
              <div key={request.id} className="SellerDashboard__tableRow">
                <div className="SellerDashboard__tableCell">{index + 1}</div>
                <div className="SellerDashboard__tableCell">{request.id}</div>
                <div className="SellerDashboard__tableCell SellerDashboard__tableCell--content">
                  <img src={request.thumbnail} alt="" className="SellerDashboard__thumbnail" />
                  <span className="SellerDashboard__contentTitle">{request.title}</span>
                </div>
                <div className="SellerDashboard__tableCell">{request.date}</div>
                <div className="SellerDashboard__tableCell">{request.price}</div>
                <div className="SellerDashboard__tableCell">{request.requestedAmount}</div>
                <div className="SellerDashboard__tableCell">{request.customer}</div>
                <div className="SellerDashboard__tableCell">
                  <button className="SellerDashboard__actionIcon">
                    <FaEye />
                  </button>
                  <button className="SellerDashboard__actionIcon">
                    <FaEdit />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* New Bids Section */}
        <div className="SellerDashboard__section">
          <div className="SellerDashboard__sectionHeader">
            <h2 className="SellerDashboard__sectionTitle">New Bids</h2>
            <button className="SellerDashboard__viewAllBtn">View All Bids</button>
          </div>

          <div className="SellerDashboard__table">
            <div className="SellerDashboard__tableHeader">
              <div className="SellerDashboard__tableHeaderCell">No.</div>
              <div className="SellerDashboard__tableHeaderCell">Bid Id</div>
              <div className="SellerDashboard__tableHeaderCell">Videos/Documents</div>
              <div className="SellerDashboard__tableHeaderCell">Date</div>
              <div className="SellerDashboard__tableHeaderCell">Price</div>
              <div className="SellerDashboard__tableHeaderCell">Bid Amount</div>
              <div className="SellerDashboard__tableHeaderCell">Action</div>
            </div>

            {newBids.map((bid, index) => (
              <div key={bid.id} className="SellerDashboard__tableRow">
                <div className="SellerDashboard__tableCell">{index + 1}</div>
                <div className="SellerDashboard__tableCell">{bid.id}</div>
                <div className="SellerDashboard__tableCell SellerDashboard__tableCell--content">
                  <img src={bid.thumbnail} alt="" className="SellerDashboard__thumbnail" />
                  <span className="SellerDashboard__contentTitle">{bid.title}</span>
                </div>
                <div className="SellerDashboard__tableCell">{bid.date}</div>
                <div className="SellerDashboard__tableCell">{bid.price}</div>
                <div className="SellerDashboard__tableCell">{bid.bidAmount}</div>
                <div className="SellerDashboard__tableCell">
                  <button className="SellerDashboard__actionIcon">
                    <FaEye />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </SellerLayout>
  );
};

export default SellerDashboard;
