import React from "react";
import { useNavigate } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import {
  selectActiveTab,
  setActiveTab,
} from "../../redux/slices/sellerDashboardSlice";
import { logout } from "../../redux/slices/authSlice";
import "../../styles/SellerSidebar.css";

// Icons
import { MdDashboard } from "react-icons/md";
import { FaUser } from "react-icons/fa";
import { FaUpload } from "react-icons/fa";
import { MdContentPaste } from "react-icons/md";
import { FaChartLine } from "react-icons/fa";
import { FaShoppingCart } from "react-icons/fa";
import { FaCog } from "react-icons/fa";
import { IoLogOut } from "react-icons/io5";

const SellerSidebar = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const activeTab = useSelector(selectActiveTab);

  // Handle tab click
  const handleTabClick = (tab) => {
    dispatch(setActiveTab(tab));
    // Navigate to the corresponding route
    switch (tab) {
      case "dashboard":
        navigate("/seller/dashboard");
        break;
      case "profile":
        navigate("/seller/profile");
        break;
      case "upload":
        navigate("/seller/upload");
        break;
      case "my-content":
        navigate("/seller/my-content");
        break;
      case "analytics":
        navigate("/seller/analytics");
        break;
      case "orders":
        navigate("/seller/orders");
        break;
      case "settings":
        navigate("/seller/settings");
        break;
      default:
        navigate("/seller/dashboard");
    }
  };

  // Handle logout
  const handleLogout = () => {
    dispatch(logout());
    navigate("/");
  };

  return (
    <div className="SellerSidebar">
      <div className="SellerSidebar__container">
        <ul className="SellerSidebar__menu">
          <li
            className={`SellerSidebar__item ${
              activeTab === "dashboard" ? "active" : ""
            }`}
            onClick={() => handleTabClick("dashboard")}
          >
            <MdDashboard className="SellerSidebar__icon" />
            <span>Dashboard</span>
          </li>

          <li
            className={`SellerSidebar__item ${
              activeTab === "profile" ? "active" : ""
            }`}
            onClick={() => handleTabClick("profile")}
          >
            <FaUser className="SellerSidebar__icon" />
            <span>My Profile</span>
          </li>

          <li
            className={`SellerSidebar__item ${
              activeTab === "upload" ? "active" : ""
            }`}
            onClick={() => handleTabClick("upload")}
          >
            <FaUpload className="SellerSidebar__icon" />
            <span>Upload Content</span>
          </li>

          <li
            className={`SellerSidebar__item ${
              activeTab === "my-content" ? "active" : ""
            }`}
            onClick={() => handleTabClick("my-content")}
          >
            <MdContentPaste className="SellerSidebar__icon" />
            <span>My Sports Strategies</span>
          </li>

          <li
            className={`SellerSidebar__item ${
              activeTab === "analytics" ? "active" : ""
            }`}
            onClick={() => handleTabClick("analytics")}
          >
            <FaChartLine className="SellerSidebar__icon" />
            <span>Analytics</span>
          </li>

          <li
            className={`SellerSidebar__item ${
              activeTab === "orders" ? "active" : ""
            }`}
            onClick={() => handleTabClick("orders")}
          >
            <FaShoppingCart className="SellerSidebar__icon" />
            <span>Orders & Sales</span>
          </li>

          <li
            className={`SellerSidebar__item ${
              activeTab === "settings" ? "active" : ""
            }`}
            onClick={() => handleTabClick("settings")}
          >
            <FaCog className="SellerSidebar__icon" />
            <span>Settings</span>
          </li>

          <li
            className="SellerSidebar__item SellerSidebar__logout"
            onClick={handleLogout}
          >
            <IoLogOut className="SellerSidebar__icon" />
            <span>Logout</span>
          </li>
        </ul>
      </div>
    </div>
  );
};

export default SellerSidebar;
