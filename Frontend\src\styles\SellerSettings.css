/* SellerSettings Component Styles */
.SellerSettings {
  display: flex;
  gap: var(--heading6);
  min-height: 500px;
}

/* Settings Navigation */
.SellerSettings__nav {
  width: 250px;
  flex-shrink: 0;
  background-color: var(--white);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-large);
  padding: var(--basefont);
  box-shadow: var(--box-shadow-light);
  height: fit-content;
}

.SellerSettings__navBtn {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  width: 100%;
  padding: var(--basefont);
  background: none;
  border: none;
  border-radius: var(--border-radius);
  color: var(--dark-gray);
  font-size: var(--basefont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: var(--extrasmallfont);
  text-align: left;
}

.SellerSettings__navBtn:hover {
  background-color: var(--primary-light-color);
  color: var(--primary-color);
}

.SellerSettings__navBtn--active {
  background-color: var(--primary-color);
  color: var(--white);
  font-weight: 600;
}

.SellerSettings__navBtn--active:hover {
  background-color: var(--btn-color);
}

.SellerSettings__navIcon {
  font-size: var(--heading6);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
}

.SellerSettings__navLabel {
  font-size: var(--basefont);
}

/* Settings Content */
.SellerSettings__content {
  flex: 1;
  background-color: var(--white);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow-light);
}

/* Section Styles */
.SellerSettings__section {
  padding: var(--heading6);
}

.SellerSettings__sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--heading6);
  padding-bottom: var(--basefont);
  border-bottom: 1px solid var(--light-gray);
}

.SellerSettings__sectionTitle {
  font-size: var(--heading5);
  font-weight: 600;
  color: var(--secondary-color);
  margin: 0;
}

.SellerSettings__editBtn {
  display: flex;
  align-items: center;
  gap: var(--extrasmallfont);
  padding: var(--extrasmallfont) var(--smallfont);
  background-color: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.SellerSettings__editBtn:hover {
  background-color: var(--btn-color);
  transform: translateY(-1px);
  box-shadow: var(--box-shadow-light);
}

/* Form Styles */
.SellerSettings__form {
  display: flex;
  flex-direction: column;
  gap: var(--heading6);
}

.SellerSettings__formGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--basefont);
}

.SellerSettings__formGroup {
  display: flex;
  flex-direction: column;
  gap: var(--extrasmallfont);
}

.SellerSettings__label {
  font-size: var(--basefont);
  font-weight: 500;
  color: var(--secondary-color);
  margin-bottom: var(--extrasmallfont);
}

.SellerSettings__input,
.SellerSettings__select {
  padding: var(--smallfont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  background-color: var(--white);
  transition: all 0.3s ease;
}

.SellerSettings__input:focus,
.SellerSettings__select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.1);
}

.SellerSettings__input:disabled,
.SellerSettings__select:disabled {
  background-color: var(--bg-gray);
  color: var(--dark-gray);
  cursor: not-allowed;
}

.SellerSettings__select {
  cursor: pointer;
}

.SellerSettings__select:disabled {
  cursor: not-allowed;
}

/* Form Actions */
.SellerSettings__formActions {
  display: flex;
  justify-content: flex-end;
  padding-top: var(--basefont);
  border-top: 1px solid var(--light-gray);
}

.SellerSettings__saveBtn {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  padding: var(--smallfont) var(--heading6);
  background-color: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.SellerSettings__saveBtn:hover {
  background-color: var(--btn-color);
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
}

/* Placeholder Content */
.SellerSettings__placeholder {
  text-align: center;
  padding: var(--heading4);
  color: var(--dark-gray);
}

.SellerSettings__placeholder p {
  font-size: var(--basefont);
  margin-bottom: var(--basefont);
  line-height: 1.6;
}

.SellerSettings__placeholder p:last-child {
  margin-bottom: 0;
}

/* Responsive Design */

/* Large Desktop (≥1200px) */
@media (min-width: 1200px) {
  .SellerSettings__nav {
    width: 280px;
  }

  .SellerSettings__formGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Tablet (768px - 1199px) */
@media (max-width: 1199px) {
  .SellerSettings__nav {
    width: 220px;
  }

  .SellerSettings__navBtn {
    padding: var(--smallfont);
    font-size: var(--smallfont);
  }

  .SellerSettings__navIcon {
    font-size: var(--basefont);
  }

  .SellerSettings__navLabel {
    font-size: var(--smallfont);
  }

  .SellerSettings__section {
    padding: var(--basefont);
  }

  .SellerSettings__sectionTitle {
    font-size: var(--heading6);
  }

  .SellerSettings__formGrid {
    grid-template-columns: 1fr;
    gap: var(--smallfont);
  }
}

/* Mobile (≤767px) */
@media (max-width: 767px) {
  .SellerSettings {
    flex-direction: column;
    gap: var(--basefont);
  }

  .SellerSettings__nav {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--extrasmallfont);
    padding: var(--smallfont);
  }

  .SellerSettings__navBtn {
    padding: var(--smallfont);
    margin-bottom: 0;
    text-align: center;
    flex-direction: column;
    gap: var(--extrasmallfont);
  }

  .SellerSettings__navIcon {
    font-size: var(--basefont);
  }

  .SellerSettings__navLabel {
    font-size: var(--extrasmallfont);
  }

  .SellerSettings__section {
    padding: var(--smallfont);
  }

  .SellerSettings__sectionHeader {
    flex-direction: column;
    align-items: stretch;
    gap: var(--smallfont);
  }

  .SellerSettings__sectionTitle {
    font-size: var(--basefont);
    text-align: center;
  }

  .SellerSettings__editBtn {
    align-self: center;
  }

  .SellerSettings__formGrid {
    grid-template-columns: 1fr;
    gap: var(--smallfont);
  }

  .SellerSettings__formActions {
    justify-content: center;
  }

  .SellerSettings__placeholder {
    padding: var(--heading6);
  }
}

/* Small Mobile (≤480px) */
@media (max-width: 480px) {
  .SellerSettings__nav {
    grid-template-columns: repeat(2, 1fr);
  }

  .SellerSettings__navBtn {
    padding: var(--extrasmallfont);
  }

  .SellerSettings__navIcon {
    font-size: var(--smallfont);
  }

  .SellerSettings__navLabel {
    font-size: var(--extrasmallfont);
  }

  .SellerSettings__section {
    padding: var(--extrasmallfont);
  }

  .SellerSettings__sectionTitle {
    font-size: var(--smallfont);
  }

  .SellerSettings__input,
  .SellerSettings__select {
    padding: var(--extrasmallfont);
    font-size: var(--smallfont);
  }

  .SellerSettings__saveBtn {
    padding: var(--extrasmallfont) var(--basefont);
    font-size: var(--smallfont);
  }

  .SellerSettings__placeholder {
    padding: var(--basefont);
  }

  .SellerSettings__placeholder p {
    font-size: var(--smallfont);
  }
}