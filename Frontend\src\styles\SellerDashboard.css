/* SellerDashboard Component Styles */
.SellerDashboard {
  display: flex;
  flex-direction: column;
  gap: var(--heading6);
}

/* Stats Grid */
.SellerDashboard__statsGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--basefont);
  margin-bottom: var(--heading6);
}

.SellerDashboard__statCard {
  background-color: var(--white);
  border-radius: var(--border-radius);
  padding: var(--heading6);
  text-align: center;
  box-shadow: var(--box-shadow-light);
  position: relative;
  overflow: hidden;
}

.SellerDashboard__statCard--purple {
  background: linear-gradient(135deg, #8B5CF6, #A855F7);
  color: var(--white);
}

.SellerDashboard__statCard--orange {
  background: linear-gradient(135deg, #F97316, #EA580C);
  color: var(--white);
}

.SellerDashboard__statCard--green {
  background: linear-gradient(135deg, #10B981, #059669);
  color: var(--white);
}

.SellerDashboard__statNumber {
  font-size: var(--heading2);
  font-weight: 700;
  margin-bottom: var(--extrasmallfont);
}

.SellerDashboard__statLabel {
  font-size: var(--basefont);
  font-weight: 500;
  opacity: 0.9;
}

/* Section Styles */
.SellerDashboard__section {
  background-color: var(--white);
  border-radius: var(--border-radius);
  padding: var(--heading6);
  box-shadow: var(--box-shadow-light);
  margin-bottom: var(--heading6);
}

.SellerDashboard__sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--basefont);
}

.SellerDashboard__sectionTitle {
  font-size: var(--heading5);
  font-weight: 600;
  color: var(--secondary-color);
  margin: 0;
}

.SellerDashboard__viewAllBtn {
  background: none;
  border: none;
  color: var(--btn-color);
  font-size: var(--smallfont);
  font-weight: 500;
  cursor: pointer;
  text-decoration: underline;
  transition: all 0.3s ease;
}

.SellerDashboard__viewAllBtn:hover {
  color: var(--primary-color);
}

/* Table Styles */
.SellerDashboard__table {
  width: 100%;
  border-collapse: collapse;
}

.SellerDashboard__tableHeader {
  display: grid;
  grid-template-columns: 50px 1fr 120px 100px 80px 80px;
  background-color: var(--bg-gray);
  border-radius: var(--border-radius);
  padding: var(--smallfont) var(--basefont);
  margin-bottom: var(--smallfont);
}

.SellerDashboard__tableHeader:nth-child(1) {
  grid-template-columns: 50px 80px 1fr 120px 100px 120px 150px 80px;
}

.SellerDashboard__tableHeader:nth-child(2) {
  grid-template-columns: 50px 80px 1fr 120px 100px 120px 80px;
}

.SellerDashboard__tableHeaderCell {
  font-size: var(--smallfont);
  font-weight: 600;
  color: var(--secondary-color);
  text-align: left;
}

.SellerDashboard__tableRow {
  display: grid;
  grid-template-columns: 50px 1fr 120px 100px 80px 80px;
  align-items: center;
  padding: var(--basefont);
  border-bottom: 1px solid var(--light-gray);
  transition: all 0.3s ease;
}

.SellerDashboard__tableRow:hover {
  background-color: var(--bg-gray);
}

.SellerDashboard__tableRow:nth-child(1) {
  grid-template-columns: 50px 80px 1fr 120px 100px 120px 150px 80px;
}

.SellerDashboard__tableRow:nth-child(2) {
  grid-template-columns: 50px 80px 1fr 120px 100px 120px 80px;
}

.SellerDashboard__tableCell {
  font-size: var(--smallfont);
  color: var(--secondary-color);
  padding: var(--extrasmallfont);
}

.SellerDashboard__tableCell--content {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.SellerDashboard__thumbnail {
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius);
  object-fit: cover;
  flex-shrink: 0;
}

.SellerDashboard__contentTitle {
  font-weight: 500;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.SellerDashboard__statusToggle {
  width: 40px;
  height: 20px;
  border-radius: 10px;
  background-color: var(--light-gray);
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
}

.SellerDashboard__statusToggle--active {
  background-color: var(--btn-color);
}

.SellerDashboard__statusToggle::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: var(--white);
  transition: all 0.3s ease;
}

.SellerDashboard__statusToggle--active::after {
  transform: translateX(20px);
}

.SellerDashboard__actionIcon {
  background: none;
  border: none;
  color: var(--dark-gray);
  font-size: var(--basefont);
  cursor: pointer;
  padding: var(--extrasmallfont);
  border-radius: var(--border-radius);
  transition: all 0.3s ease;
  margin-right: var(--extrasmallfont);
}

.SellerDashboard__actionIcon:hover {
  background-color: var(--bg-gray);
  color: var(--btn-color);
}

/* Responsive Design */

/* Tablet (768px - 1199px) */
@media (max-width: 1199px) {
  .SellerDashboard__statsGrid {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--smallfont);
  }

  .SellerDashboard__statCard {
    padding: var(--basefont);
  }

  .SellerDashboard__statNumber {
    font-size: var(--heading3);
  }

  .SellerDashboard__statLabel {
    font-size: var(--smallfont);
  }

  .SellerDashboard__section {
    padding: var(--basefont);
  }

  .SellerDashboard__sectionTitle {
    font-size: var(--heading6);
  }

  .SellerDashboard__tableHeader,
  .SellerDashboard__tableRow {
    grid-template-columns: 40px 1fr 100px 80px 60px 60px;
    padding: var(--smallfont);
  }

  .SellerDashboard__thumbnail {
    width: 32px;
    height: 32px;
  }
}

/* Mobile (≤767px) */
@media (max-width: 767px) {
  .SellerDashboard {
    gap: var(--basefont);
  }

  .SellerDashboard__statsGrid {
    grid-template-columns: 1fr;
    gap: var(--smallfont);
    margin-bottom: var(--basefont);
  }

  .SellerDashboard__statCard {
    padding: var(--smallfont);
  }

  .SellerDashboard__statNumber {
    font-size: var(--heading4);
  }

  .SellerDashboard__statLabel {
    font-size: var(--smallfont);
  }

  .SellerDashboard__section {
    padding: var(--smallfont);
    margin-bottom: var(--basefont);
  }

  .SellerDashboard__sectionHeader {
    flex-direction: column;
    align-items: stretch;
    gap: var(--smallfont);
  }

  .SellerDashboard__sectionTitle {
    font-size: var(--basefont);
    text-align: center;
  }

  .SellerDashboard__viewAllBtn {
    align-self: center;
  }

  .SellerDashboard__table {
    overflow-x: auto;
  }

  .SellerDashboard__tableHeader,
  .SellerDashboard__tableRow {
    grid-template-columns: 30px 1fr 80px 60px 50px 50px;
    padding: var(--extrasmallfont);
    font-size: var(--extrasmallfont);
  }

  .SellerDashboard__tableHeaderCell,
  .SellerDashboard__tableCell {
    font-size: var(--extrasmallfont);
  }

  .SellerDashboard__thumbnail {
    width: 24px;
    height: 24px;
  }

  .SellerDashboard__contentTitle {
    font-size: var(--extrasmallfont);
    -webkit-line-clamp: 1;
  }

  .SellerDashboard__statusToggle {
    width: 30px;
    height: 16px;
  }

  .SellerDashboard__statusToggle::after {
    width: 12px;
    height: 12px;
  }

  .SellerDashboard__statusToggle--active::after {
    transform: translateX(14px);
  }

  .SellerDashboard__actionIcon {
    font-size: var(--smallfont);
    padding: var(--extrasmallfont);
  }
}

/* Small Mobile (≤480px) */
@media (max-width: 480px) {
  .SellerDashboard__statsGrid {
    margin-bottom: var(--smallfont);
  }

  .SellerDashboard__statNumber {
    font-size: var(--heading5);
  }

  .SellerDashboard__statLabel {
    font-size: var(--extrasmallfont);
  }

  .SellerDashboard__section {
    padding: var(--extrasmallfont);
  }

  .SellerDashboard__sectionTitle {
    font-size: var(--smallfont);
  }

  .SellerDashboard__tableHeader,
  .SellerDashboard__tableRow {
    grid-template-columns: 25px 1fr 60px 50px 40px 40px;
  }

  .SellerDashboard__thumbnail {
    width: 20px;
    height: 20px;
  }

  .SellerDashboard__statusToggle {
    width: 24px;
    height: 12px;
  }

  .SellerDashboard__statusToggle::after {
    width: 8px;
    height: 8px;
  }

  .SellerDashboard__statusToggle--active::after {
    transform: translateX(12px);
  }
}

