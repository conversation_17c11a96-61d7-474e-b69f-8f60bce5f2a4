/* SellerMyContent Component Styles */
.SellerMyContent {
  display: flex;
  flex-direction: column;
  gap: var(--heading6);
}

/* Header Controls */
.SellerMyContent__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--basefont);
  margin-bottom: var(--heading6);
}

.SellerMyContent__headerLeft {
  display: flex;
  align-items: center;
}

.SellerMyContent__addBtn {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  padding: var(--smallfont) var(--basefont);
  background-color: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.SellerMyContent__addBtn:hover {
  background-color: var(--btn-color);
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
}

.SellerMyContent__headerRight {
  display: flex;
  align-items: center;
  gap: var(--basefont);
}

/* Search Box */
.SellerMyContent__searchBox {
  position: relative;
  display: flex;
  align-items: center;
}

.SellerMyContent__searchIcon {
  position: absolute;
  left: var(--smallfont);
  color: var(--dark-gray);
  font-size: var(--smallfont);
  z-index: 1;
}

.SellerMyContent__searchInput {
  padding: var(--smallfont) var(--smallfont) var(--smallfont) var(--heading6);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  width: 250px;
  transition: all 0.3s ease;
}

.SellerMyContent__searchInput:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.1);
}

/* Filter Box */
.SellerMyContent__filterBox {
  position: relative;
  display: flex;
  align-items: center;
}

.SellerMyContent__filterIcon {
  position: absolute;
  left: var(--smallfont);
  color: var(--dark-gray);
  font-size: var(--smallfont);
  z-index: 1;
}

.SellerMyContent__filterSelect {
  padding: var(--smallfont) var(--smallfont) var(--smallfont) var(--heading6);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  background-color: var(--white);
  cursor: pointer;
  transition: all 0.3s ease;
}

.SellerMyContent__filterSelect:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.1);
}

/* View Toggle */
.SellerMyContent__viewToggle {
  display: flex;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.SellerMyContent__viewBtn {
  padding: var(--smallfont);
  background-color: var(--white);
  border: none;
  color: var(--dark-gray);
  font-size: var(--basefont);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.SellerMyContent__viewBtn:hover {
  background-color: var(--bg-gray);
  color: var(--secondary-color);
}

.SellerMyContent__viewBtn--active {
  background-color: var(--primary-color);
  color: var(--white);
}

.SellerMyContent__viewBtn--active:hover {
  background-color: var(--btn-color);
}

/* Content Stats */
.SellerMyContent__stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--basefont);
  margin-bottom: var(--heading6);
}

.SellerMyContent__statItem {
  background-color: var(--white);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  padding: var(--basefont);
  text-align: center;
  box-shadow: var(--box-shadow-light);
  transition: all 0.3s ease;
}

.SellerMyContent__statItem:hover {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
}

.SellerMyContent__statValue {
  display: block;
  font-size: var(--heading4);
  font-weight: 700;
  color: var(--secondary-color);
  margin-bottom: var(--extrasmallfont);
}

.SellerMyContent__statLabel {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  font-weight: 500;
}

/* Content Grid/List */
.SellerMyContent__content {
  display: grid;
  gap: var(--basefont);
}

.SellerMyContent__content--grid {
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

.SellerMyContent__content--list {
  grid-template-columns: 1fr;
}

/* Content Item */
.SellerMyContent__item {
  background-color: var(--white);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-large);
  overflow: hidden;
  box-shadow: var(--box-shadow-light);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
}

.SellerMyContent__content--list .SellerMyContent__item {
  flex-direction: row;
  align-items: center;
}

.SellerMyContent__item:hover {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
  border-color: var(--primary-color);
}

/* Thumbnail */
.SellerMyContent__thumbnail {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
  background-color: var(--bg-gray);
}

.SellerMyContent__content--list .SellerMyContent__thumbnail {
  width: 150px;
  height: 100px;
  flex-shrink: 0;
}

.SellerMyContent__thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.SellerMyContent__thumbnailPlaceholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--primary-color), var(--btn-color));
  color: var(--white);
  font-size: var(--heading6);
  font-weight: 600;
}

.SellerMyContent__statusBadge {
  position: absolute;
  top: var(--smallfont);
  right: var(--smallfont);
  padding: var(--extrasmallfont) var(--smallfont);
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.SellerMyContent__statusBadge--active {
  background-color: #4CAF50;
  color: var(--white);
}

.SellerMyContent__statusBadge--draft {
  background-color: #FF9800;
  color: var(--white);
}

.SellerMyContent__statusBadge--default {
  background-color: var(--dark-gray);
  color: var(--white);
}

/* Content Info */
.SellerMyContent__info {
  padding: var(--basefont);
  flex: 1;
}

.SellerMyContent__title {
  font-size: var(--heading6);
  font-weight: 600;
  color: var(--secondary-color);
  margin: 0 0 var(--extrasmallfont) 0;
  line-height: 1.3;
}

.SellerMyContent__category {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  margin: 0 0 var(--smallfont) 0;
}

.SellerMyContent__meta {
  display: flex;
  flex-wrap: wrap;
  gap: var(--basefont);
  margin-bottom: var(--smallfont);
}

.SellerMyContent__price {
  font-size: var(--basefont);
  font-weight: 600;
  color: var(--primary-color);
}

.SellerMyContent__sales {
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

.SellerMyContent__revenue {
  font-size: var(--smallfont);
  font-weight: 600;
  color: #4CAF50;
}

.SellerMyContent__date {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
  margin: 0;
}

/* Actions */
.SellerMyContent__actions {
  display: flex;
  gap: var(--extrasmallfont);
  padding: var(--basefont);
  border-top: 1px solid var(--light-gray);
}

.SellerMyContent__content--list .SellerMyContent__actions {
  border-top: none;
  border-left: 1px solid var(--light-gray);
  flex-direction: column;
  padding: var(--smallfont);
}

.SellerMyContent__actionBtn {
  padding: var(--extrasmallfont);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
}

.SellerMyContent__actionBtn--view {
  background-color: var(--bg-blue);
  color: #2196F3;
}

.SellerMyContent__actionBtn--view:hover {
  background-color: #2196F3;
  color: var(--white);
}

.SellerMyContent__actionBtn--edit {
  background-color: var(--primary-light-color);
  color: var(--primary-color);
}

.SellerMyContent__actionBtn--edit:hover {
  background-color: var(--primary-color);
  color: var(--white);
}

.SellerMyContent__actionBtn--delete {
  background-color: #ffebee;
  color: #f44336;
}

.SellerMyContent__actionBtn--delete:hover {
  background-color: #f44336;
  color: var(--white);
}

/* Empty State */
.SellerMyContent__empty {
  grid-column: 1 / -1;
  text-align: center;
  padding: var(--heading4);
  background-color: var(--white);
  border: 2px dashed var(--light-gray);
  border-radius: var(--border-radius-large);
}

.SellerMyContent__empty p {
  font-size: var(--heading6);
  color: var(--dark-gray);
  margin-bottom: var(--basefont);
}

.SellerMyContent__emptyBtn {
  display: inline-flex;
  align-items: center;
  gap: var(--smallfont);
  padding: var(--basefont) var(--heading6);
  background-color: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.SellerMyContent__emptyBtn:hover {
  background-color: var(--btn-color);
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
}

/* Responsive Design */

/* Large Desktop (≥1200px) */
@media (min-width: 1200px) {
  .SellerMyContent__content--grid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }

  .SellerMyContent__searchInput {
    width: 300px;
  }
}

/* Tablet (768px - 1199px) */
@media (max-width: 1199px) {
  .SellerMyContent__header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--smallfont);
  }

  .SellerMyContent__headerRight {
    justify-content: space-between;
  }

  .SellerMyContent__searchInput {
    width: 200px;
  }

  .SellerMyContent__stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .SellerMyContent__content--grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
}

/* Mobile (≤767px) */
@media (max-width: 767px) {
  .SellerMyContent {
    gap: var(--basefont);
  }

  .SellerMyContent__header {
    gap: var(--smallfont);
  }

  .SellerMyContent__headerRight {
    flex-direction: column;
    gap: var(--smallfont);
  }

  .SellerMyContent__searchInput {
    width: 100%;
  }

  .SellerMyContent__stats {
    grid-template-columns: 1fr;
    gap: var(--smallfont);
  }

  .SellerMyContent__statItem {
    padding: var(--smallfont);
  }

  .SellerMyContent__statValue {
    font-size: var(--heading5);
  }

  .SellerMyContent__content--grid {
    grid-template-columns: 1fr;
  }

  .SellerMyContent__item {
    margin-bottom: var(--smallfont);
  }

  .SellerMyContent__thumbnail {
    height: 150px;
  }

  .SellerMyContent__info {
    padding: var(--smallfont);
  }

  .SellerMyContent__title {
    font-size: var(--basefont);
  }

  .SellerMyContent__meta {
    flex-direction: column;
    gap: var(--extrasmallfont);
  }

  .SellerMyContent__actions {
    padding: var(--smallfont);
    justify-content: center;
  }
}

/* Small Mobile (≤480px) */
@media (max-width: 480px) {
  .SellerMyContent__addBtn {
    width: 100%;
    justify-content: center;
  }

  .SellerMyContent__headerRight {
    width: 100%;
  }

  .SellerMyContent__viewToggle {
    width: 100%;
  }

  .SellerMyContent__viewBtn {
    flex: 1;
  }

  .SellerMyContent__stats {
    margin-bottom: var(--basefont);
  }

  .SellerMyContent__statValue {
    font-size: var(--heading6);
  }

  .SellerMyContent__thumbnail {
    height: 120px;
  }

  .SellerMyContent__actions {
    gap: var(--smallfont);
  }

  .SellerMyContent__actionBtn {
    flex: 1;
    min-width: auto;
  }
}

/* List View Specific Responsive */
@media (max-width: 767px) {
  .SellerMyContent__content--list .SellerMyContent__item {
    flex-direction: column;
  }

  .SellerMyContent__content--list .SellerMyContent__thumbnail {
    width: 100%;
    height: 150px;
  }

  .SellerMyContent__content--list .SellerMyContent__actions {
    border-left: none;
    border-top: 1px solid var(--light-gray);
    flex-direction: row;
    padding: var(--smallfont);
  }
}