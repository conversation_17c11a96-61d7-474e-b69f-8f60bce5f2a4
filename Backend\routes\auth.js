const express = require('express');
const { check } = require('express-validator');
const {
  register,
  login,
  sendOTP,
  verifyOTP,
  getMe,
  updateMe,
  logout,
  verifyEmail,
  googleAuth,
  googleSignup
} = require('../controllers/auth');

const { protect } = require('../middleware/auth');
const upload = require('../utils/fileUpload');

const router = express.Router();

router.post(
  '/register',
  [
    check('firstName', 'First name is required').not().isEmpty(),
    check('lastName', 'Last name is required').not().isEmpty(),
    check('email', 'Please include a valid email').isEmail(),
    check('mobile', 'Mobile number is required').not().isEmpty(),
    check('role', 'Role must be either buyer or seller').isIn(['buyer', 'seller'])
  ],
  register
);

router.post(
  '/login',
  [
    check('mobile', 'Mobile number is required').not().isEmpty()
  ],
  login
);

router.post(
  '/send-otp',
  [
    check('email', 'Please include a valid email').optional().isEmail(),
    check('mobile', 'Mobile number is required').optional().not().isEmpty(),
    check('userId', 'User ID for resend').optional().not().isEmpty()
  ],
  sendOTP
);

router.post(
  '/verify-otp',
  [
    check('userId', 'User ID is required').not().isEmpty(),
    check('otp', 'OTP is required').not().isEmpty()
  ],
  verifyOTP
);

router.get('/me', protect, getMe);
router.put(
  '/update',
  protect,
  [
    check('firstName', 'First name is required').not().isEmpty(),
    check('lastName', 'Last name is required').not().isEmpty(),
    check('profileImage', 'Profile image must be a string').optional().isString()
  ],
  updateMe
);
router.get('/logout', protect, logout);
router.get('/verify-email/:token', verifyEmail);

// Google Authentication Routes
router.post(
  '/google',
  [
    check('idToken', 'Firebase ID token is required').not().isEmpty()
  ],
  googleAuth
);

router.post(
  '/google-signup',
  [
    check('idToken', 'Firebase ID token is required').not().isEmpty(),
    check('role', 'Role must be either buyer or seller').isIn(['buyer', 'seller'])
  ],
  googleSignup
);

// Profile image upload route
router.post(
  '/upload',
  protect,
  upload.single('profileImage'),
  (req, res) => {
    res.status(200).json({
      success: true,
      data: {
        fileUrl: req.file.location || `/uploads/${req.file.filename}`,
        fileName: req.file.originalname,
        fileType: req.file.mimetype,
        fileSize: req.file.size
      }
    });
  }
);

module.exports = router;
